#!/usr/bin/env python3
"""
扣子(Coze) 工作流流式响应API调用脚本
支持流式响应解析、错误处理和自定义参数配置
"""

import json
import requests
import time
from typing import Dict, Any, Optional, Generator
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CozeWorkflowClient:
    """扣子工作流客户端"""
    
    def __init__(self, api_token: str, base_url: str = "https://api.coze.cn"):
        """
        初始化客户端
        
        Args:
            api_token: 扣子API访问令牌
            base_url: API基础URL
        """
        self.api_token = api_token
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/json'
        })
    
    def stream_run_workflow(
        self,
        workflow_id: str,
        parameters: Dict[str, Any],
        bot_id: Optional[str] = None,
        app_id: Optional[str] = None,
        ext: Optional[Dict[str, str]] = None,
        workflow_version: Optional[str] = None,
        timeout: int = 300
    ) -> Generator[Dict[str, Any], None, None]:
        """
        执行工作流（流式响应）
        
        Args:
            workflow_id: 工作流ID
            parameters: 工作流输入参数
            bot_id: 关联的智能体ID（可选）
            app_id: 关联的应用ID（可选）
            ext: 额外字段（可选）
            workflow_version: 工作流版本（可选）
            timeout: 请求超时时间（秒）
            
        Yields:
            Dict[str, Any]: 流式响应事件数据
            
        Raises:
            ValueError: 参数错误
            requests.RequestException: 网络请求错误
        """
        # 参数验证
        if not workflow_id:
            raise ValueError("workflow_id 不能为空")
        
        if bot_id and app_id:
            raise ValueError("不能同时指定 bot_id 和 app_id")
        
        # 构建请求数据
        payload = {
            "workflow_id": workflow_id,
            "parameters": parameters or {}
        }
        
        if bot_id:
            payload["bot_id"] = bot_id
        if app_id:
            payload["app_id"] = app_id
        if ext:
            payload["ext"] = ext
        if workflow_version:
            payload["workflow_version"] = workflow_version
        
        url = f"{self.base_url}/v1/workflow/stream_run"
        
        logger.info(f"开始执行工作流: {workflow_id}")
        logger.debug(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            # 发送流式请求
            response = self.session.post(
                url,
                json=payload,
                stream=True,
                timeout=timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                error_msg = f"HTTP错误 {response.status_code}: {response.text}"
                logger.error(error_msg)
                raise requests.RequestException(error_msg)
            
            # 解析流式响应
            for event_data in self._parse_stream_response(response):
                yield event_data
                
        except requests.Timeout:
            logger.error(f"请求超时 ({timeout}秒)")
            raise
        except requests.RequestException as e:
            logger.error(f"网络请求错误: {e}")
            raise
        except Exception as e:
            logger.error(f"未知错误: {e}")
            raise
    
    def _parse_stream_response(self, response: requests.Response) -> Generator[Dict[str, Any], None, None]:
        """
        解析流式响应数据
        
        Args:
            response: HTTP响应对象
            
        Yields:
            Dict[str, Any]: 解析后的事件数据
        """
        buffer = ""
        
        try:
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if not chunk:
                    continue
                
                buffer += chunk
                
                # 按行分割处理
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if not line:
                        continue
                    
                    # 解析Server-Sent Events格式
                    event_data = self._parse_sse_line(line)
                    if event_data:
                        yield event_data
                        
                        # 检查是否为结束事件
                        if event_data.get('event') == 'Done':
                            logger.info("工作流执行完成")
                            return
                            
        except Exception as e:
            logger.error(f"解析流式响应时出错: {e}")
            raise
    
    def _parse_sse_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        解析单行SSE数据
        
        Args:
            line: SSE数据行
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的事件数据，如果解析失败返回None
        """
        try:
            if line.startswith('data: '):
                data_str = line[6:]  # 移除 'data: ' 前缀
                
                if data_str.strip() == '[DONE]':
                    return {'event': 'Done', 'data': None}
                
                try:
                    data = json.loads(data_str)
                    return data
                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON数据: {data_str}")
                    return None
                    
            elif line.startswith('event: '):
                event_type = line[7:]  # 移除 'event: ' 前缀
                return {'event': event_type, 'data': None}
                
        except Exception as e:
            logger.warning(f"解析SSE行时出错: {e}, 行内容: {line}")
            
        return None


def get_default_config() -> Dict[str, Any]:
    """
    获取默认配置参数

    Returns:
        Dict[str, Any]: 默认配置参数
    """
    return {
        "api_token": "cztei_hnp3MAFwH2D0ldrq99Ns34lGVoP3bgreSNdSVcj77S8zbQ9e01NQ4MFlxn8PIbqtf",
        "base_url": "https://api.coze.cn",
        "workflow_id": "7532396941256278052",
        "parameters": {
            "LOGO": "https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/f9d26237732441398a3035d625687356.png~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1784879130&x-signature=jDtIT8r%2BqqID2cLkn%2BQ005t4KEk%3D&x-wf-file_name=0.png",
            "serviceName": "空调清洗"
        },
        "optional_params": {
            "bot_id": None,
            "app_id": None,
            "ext": None,
            "workflow_version": None,
            "timeout": 300
        }
    }


def main():
    """主函数 - 示例用法"""

    # 获取配置
    config = get_default_config()

    # 配置参数
    API_TOKEN = config["api_token"]
    WORKFLOW_ID = config["workflow_id"]
    BASE_URL = config["base_url"]

    # 工作流参数
    parameters = config["parameters"]

    # 可选参数
    optional_params = config["optional_params"]

    # 创建客户端
    client = CozeWorkflowClient(API_TOKEN, BASE_URL)
    
    try:
        logger.info("开始执行工作流...")

        # 执行工作流并处理流式响应
        for event in client.stream_run_workflow(
            workflow_id=WORKFLOW_ID,
            parameters=parameters,
            bot_id=optional_params.get("bot_id"),
            app_id=optional_params.get("app_id"),
            ext=optional_params.get("ext"),
            workflow_version=optional_params.get("workflow_version"),
            timeout=optional_params.get("timeout", 300)
        ):
            # 处理不同类型的事件
            event_type = event.get('event', 'unknown')
            
            if event_type == 'Done':
                logger.info("✅ 工作流执行完成")
                break
            elif event_type == 'Error':
                logger.error(f"❌ 工作流执行错误: {event.get('data', {})}")
                break
            else:
                # 打印事件数据
                print(f"📨 事件类型: {event_type}")
                if event.get('data'):
                    print(f"📄 事件数据: {json.dumps(event['data'], ensure_ascii=False, indent=2)}")
                print("-" * 50)
                
    except Exception as e:
        logger.error(f"执行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
